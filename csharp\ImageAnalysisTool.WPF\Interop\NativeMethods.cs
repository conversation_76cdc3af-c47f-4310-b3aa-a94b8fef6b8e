using System.Runtime.InteropServices;

namespace ImageAnalysisTool.WPF.Interop
{
    [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
    internal delegate void ProgressCallback(int progress);

    internal static class NativeMethods
    {
        private const string DllPath = "ImageProcessor.dll";

        // 结构体定义必须与 C++ 中的定义完全匹配
        // [StructLayout(LayoutKind.Sequential)]
        // internal struct ParallelismResult
        // {
        //     public bool isParallel;
        //     public float inlierRatio;
        // }

        [DllImport(DllPath, CallingConvention = CallingConvention.Cdecl)]
        internal static extern int ProcessImage_Canny(
            byte[] inputImageData,
            byte[] outputImageData,
            int width,
            int height,
            double threshold1,
            double threshold2,
            int roiX,
            int roiY,
            int roiWidth,
            int roiHeight
        );

        [DllImport(DllPath, CallingConvention = CallingConvention.Cdecl)]
        internal static extern int AnalyzeParallelism(
            [MarshalAs(UnmanagedType.LPStr)] string imagePath,
            int roiX,
            int roiY,
            int roiWidth,
            int roiHeight,
            int thresholdStart,
            int thresholdEnd,
            int thresholdStep,
            ProgressCallback callback,
            byte[] outImageBuffer,
            int outImageBufferSize
        );

        [DllImport(DllPath, CallingConvention = CallingConvention.Cdecl)]
        internal static extern int AnalyzeCurvesFromProcessedImage(
            byte[] processedImageData,
            byte[] originalRoiImageData,
            int width,
            int height,
            byte[] outImageBuffer,
            int outImageBufferSize
        );
    }
}