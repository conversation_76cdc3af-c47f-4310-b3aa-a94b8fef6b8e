// 文件名: ImageProcessor.h
#pragma once
#ifdef IMAGEPROCESSOR_EXPORTS
#define API __declspec(dllexport)
#else
#define API __declspec(dllimport)
#endif

// 定义用于返回给 C# 的结果结构体
struct ParallelismResult {
    bool isParallel;
    float inlierRatio;
    // 可以添加其他需要返回的数据，例如平均距离等
};

// 定义回调函数指针类型，用于向 C# 报告进度
typedef void (*ProgressCallback)(int progress);

extern "C" {
    API int ProcessImage_Canny(
        unsigned char* inputImageData,
        unsigned char* outputImageData,
        int width,
        int height,
        double threshold1,
        double threshold2,
        int roiX,
        int roiY,
        int roiWidth,
        int roiHeight
    );

    // 新的分析函数
    API int AnalyzeParallelism(
        const char* imagePath,
        int roiX,
        int roiY,
        int roiWidth,
        int roiHeight,
        int thresholdStart,
        int thresholdEnd,
        int thresholdStep,
        ProgressCallback callback,
        unsigned char* outImageBuffer,
        int outImageBufferSize
    );

    // 基于Processed Image的曲线拟合函数
    API int AnalyzeCurvesFromProcessedImage(
        unsigned char* processedImageData,
        unsigned char* originalRoiImageData, // 新增：用于绘图背景的原始ROI图像数据
        int width,
        int height,
        unsigned char* outImageBuffer,
        int outImageBufferSize
    );
}