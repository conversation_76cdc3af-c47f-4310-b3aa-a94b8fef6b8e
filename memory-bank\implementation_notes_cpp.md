# C++ Implementation Notes

This document details the implementation of the C++ image processing backend.

---
### Decision (Code)
[2025-07-10 09:09:42] - Implemented a complete end-to-end algorithm for parallelism analysis based on the `TODO` file specifications.

**Rationale:**
The goal was to create a robust function that encapsulates the entire image analysis pipeline, from image loading to a final parallelism judgment. This approach provides a clean, high-level API for the C# frontend.

**Details:**

**1. Exported Function & Data Structure:**

A new function `AnalyzeParallelism` was added and exported. It takes an image file path and an ROI as input and returns a `ParallelismResult` struct.

*File: `cpp/include/ImageProcessor.h`*
```cpp
// 定义用于返回给 C# 的结果结构体
struct ParallelismResult {
    bool isParallel;
    float inlierRatio;
};

extern "C" {
    // ... existing ProcessImage_Canny ...

    // 新的分析函数
    API ParallelismResult AnalyzeParallelism(
        const char* imagePath,
        int roiX,
        int roiY,
        int roiWidth,
        int roiHeight
    );
}
```

**2. Algorithm Pipeline:**

The implementation in `cpp/src/ImageProcessor.cpp` follows the three stages outlined in the `TODO` document:

*   **Stage 1: Curve Extraction**
    *   Loads the image from the specified path.
    *   Applies a **Bilateral Filter** for noise reduction while preserving edges.
    *   Uses **Canny Edge Detection** to find sharp edges.
    *   Performs a **Morphological Closing** operation to connect broken edge segments.
    *   Finds all contours, filters them by a minimum length to discard noise, and sorts the remaining contours by their average Y-position to identify the top (`curve_A`) and bottom (`curve_B`) curves.

*   **Stage 2: Geometric Feature Calculation**
    *   A helper function `computeNormalsPCA` was implemented.
    *   For each point on `curve_A`, it analyzes a local neighborhood of points using **Principal Component Analysis (PCA)**.
    *   The eigenvector corresponding to the smallest eigenvalue is taken as the normal vector at that point.
    *   Normals are normalized and consistently oriented (Y-component is made positive).

*   **Stage 3: Parallelism Judgment**
    *   A **RANSAC (Random Sample Consensus)** algorithm is implemented to robustly determine parallelism.
    *   **Iterations:** The algorithm runs for a fixed number of iterations (e.g., 200).
    *   **Modeling:** In each iteration, it randomly samples a point from `curve_A` and finds its nearest neighbor on `curve_B` to define a "parallel distance" model.
    *   **Consensus:** It then counts how many points on `curve_A` (the "inliers") fit this model within a given tolerance. A point fits the model if its position, shifted along its normal vector by the model distance, is close to `curve_B`.
    *   **Result:** The final `inlierRatio` (best inlier count / total points) is calculated. If this ratio exceeds a threshold (e.g., 90%), the curves are considered parallel.

**Key Parameters (requiring potential tuning):**
*   Canny thresholds
*   Minimum contour length
*   PCA neighborhood size
*   RANSAC iterations
*   RANSAC distance tolerance
*   RANSAC inlier ratio threshold

---
### Decision (Code)
[2025-07-10 09:46:28] - Refactor `AnalyzeParallelism` to use a multi-threshold scanning technique.

**Rationale:**
The previous implementation used a single, fixed threshold for binarization, which was not robust enough to extract complete curves from images with varying contrast or illumination. The new approach iterates through a range of thresholds (50 to 200), detects contours at each level, and fuses all resulting points into a single "master point set". This ensures that all parts of the curves are captured, regardless of their specific gray level.

**Details:**
- **Algorithm Change:** Replaced the single Canny edge detection step with a loop.
- **Loop:** Iterates from a grayscale threshold of 50 to 200, with a step of 10.
- **Point Fusion:** Inside the loop, `cv::threshold` is used to binarize the image, `cv::findContours` extracts curves, and all points from all contours are appended to a `std::vector<cv::Point>` called `masterPointSet`.
- **Curve Separation:** After the loop, the `masterPointSet` is sorted by the y-coordinate. A split point is found by identifying the largest gap in y-coordinates between consecutive points. This gap effectively separates the upper and lower curves.
- **Finalization:** The points are split into `curve_A` and `curve_B` based on the split index. Each curve is then sorted by its x-coordinate to ensure correct point order for the subsequent PCA and RANSAC analysis.
- **File Reference:** `cpp/src/ImageProcessor.cpp`

---
### [2025-07-10] - `AnalyzeParallelism` 增强：自定义参数与进度回调

**变更概述:**
为了提供更灵活的分析能力和更好的用户体验，`AnalyzeParallelism` 函数进行了重大升级。现在它支持调用者（C#）自定义阈值扫描范围，并通过回调函数接收实时的处理进度。

**新函数签名 (`ImageProcessor.h`):**
```cpp
// 定义回调函数指针类型，用于向 C# 报告进度
typedef void (*ProgressCallback)(int progress);

API ParallelismResult AnalyzeParallelism(
    const char* imagePath,
    int roiX,
    int roiY,
    int roiWidth,
    int roiHeight,
    int thresholdStart,  // 扫描起始阈值
    int thresholdEnd,    // 扫描结束阈值
    int thresholdStep,   // 扫描步长
    ProgressCallback callback // 进度回调函数指针
);
```

**实现细节 (`ImageProcessor.cpp`):**
1.  **自定义扫描循环:** 函数内部的硬编码阈值循环 (`for (int threshold = 50; ...)` ) 已被替换，现在使用 `thresholdStart`, `thresholdEnd`, 和 `thresholdStep` 参数来驱动循环。
2.  **进度计算与回调:**
    *   在循环的每次迭代中，会根据当前阈值计算进度百分比：`progress = (current - start) * 100 / (end - start)`。
    *   在调用回调之前，会检查 `callback` 指针是否为 `nullptr`，以防止在调用方未提供回调时发生崩溃。
    *   `callback(progress)` 被调用以将 0-100 的整数进度值发送回 C# 层。
    *   在发生错误（如无法加载图像）时，会调用 `callback(-1)` 来向调用方发出错误信号。

**C# 交互:**
C# 端现在需要：
1.  定义一个与 `ProgressCallback` 签名匹配的委托。
2.  创建一个该委托的实例，该实例指向一个能够更新 UI（如 `ProgressBar`）的方法。
3.  在调用 `AnalyzeParallelism` 的 P/Invoke 方法时，将此委托实例作为参数传递。

---
### [2025-07-10] - `AnalyzeParallelism` 增强：可视化曲线拟合与图像返回

**变更概述:**
为了给用户提供关于算法内部识别结果的直观反馈，`AnalyzeParallelism` 函数现在具备了可视化功能。它可以在分析完成后，返回一张绘制有拟合曲线的图像。此功能通过修改函数接口，使用调用者提供的缓冲区来传递图像数据实现。

**新函数签名 (`ImageProcessor.h`):**
```cpp
API int AnalyzeParallelism(
    const char* imagePath,
    int roiX,
    int roiY,
    int roiWidth,
    int roiHeight,
    int thresholdStart,
    int thresholdEnd,
    int thresholdStep,
    ProgressCallback callback,
    unsigned char* outImageBuffer, // 新增：用于接收图像数据的缓冲区
    int outImageBufferSize       // 新增：缓冲区大小
);
```

**实现细节 (`ImageProcessor.cpp`):**
1.  **圆弧拟合:**
    *   在通过多阈值扫描并成功分离出上下两条曲线的点集 (`curve_A`, `curve_B`) 后，如果点集包含至少5个点，则调用 `cv::fitEllipse` 为每个点集拟合一个旋转矩形（椭圆）。

2.  **结果绘制:**
    *   函数首先创建输入图像的一份彩色副本 (`visImage`)。
    *   拟合出的两个椭圆分别使用亮绿色 (`cv::Scalar(0, 255, 0)`) 和亮蓝色 (`cv::Scalar(255, 0, 0)`) 通过 `cv::ellipse` 函数绘制到 `visImage` 上。
    *   **重要:** 绘制时，椭圆的中心坐标会加上 ROI 的左上角偏移量 (`roiRect.x`, `roiRect.y`)，以确保它们被绘制在原图的正确位置上。

3.  **图像编码与返回:**
    *   `ParallelismResult` 结构体不再被返回。函数现在返回一个 `int` 值。
    *   绘制完成后，`visImage` 使用 `cv::imencode` 被编码为 `.jpg` 格式，并存入一个临时的 `std::vector<unsigned char>`。
    *   **数据传递:**
        *   如果编码后的数据大小小于或等于 `outImageBufferSize`，数据将被复制到 `outImageBuffer` 指针指向的内存中，函数返回实际写入的字节数（一个正整数）。
        *   如果缓冲区太小，函数返回 `-1`。
        *   如果图像编码失败，函数返回 `-2`。
        *   其他负值表示处理流程中其他步骤的错误。

**C# 交互:**
C# 调用端现在需要：
1.  更新 P/Invoke 签名以匹配新的 C++ 函数。
2.  在调用函数前，创建一个足够大的 `byte[]` 数组作为缓冲区。
3.  检查返回的 `int` 值。如果大于零，则将缓冲区中指定长度的数据加载到 `MemoryStream` 中，并最终创建 `BitmapImage` 以在 UI 上显示。