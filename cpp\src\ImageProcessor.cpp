#include "../include/ImageProcessor.h"
#include <opencv2/core.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/highgui.hpp> // For imread
#include <iostream>
#include <vector>
#include <algorithm>
#include <map>
#include <utility>
#include <memory>
#include <limits>

// Define IMAGEPROCESSOR_EXPORTS when building the DLL
// This should typically be done in the project settings

extern "C" {
    API int ProcessImage_Canny(
        unsigned char* inputImageData,
        unsigned char* outputImageData,
        int width,
        int height,
        double threshold1,
        double threshold2,
        int roiX,
        int roiY,
        int roiWidth,
        int roiHeight
    ) {
        try {
            std::cout << "ProcessImage_Canny called with:" << std::endl;
            std::cout << "  Image size: " << width << "x" << height << std::endl;
            std::cout << "  ROI: (" << roiX << ", " << roiY << ", " << roiWidth << ", " << roiHeight << ")" << std::endl;
            std::cout << "  Thresholds: " << threshold1 << ", " << threshold2 << std::endl;

            // 1. Wrap the full input data in a cv::Mat
            cv::Mat fullImage(height, width, CV_8UC4, inputImageData);

            // 2. Create a cv::Rect for the ROI and perform bounds checking
            cv::Rect roiRect(roiX, roiY, roiWidth, roiHeight);
            
            // Ensure the ROI is within the image boundaries
            roiRect &= cv::Rect(0, 0, width, height);

            if (roiRect.width <= 0 || roiRect.height <= 0) {
                std::cout << "ERROR: Invalid ROI after bounds checking: " << roiRect.width << "x" << roiRect.height << std::endl;
                return -2; // Indicate invalid ROI
            }

            std::cout << "  Final ROI after bounds checking: (" << roiRect.x << ", " << roiRect.y << ", " << roiRect.width << ", " << roiRect.height << ")" << std::endl;

            // 3. Extract the ROI from the full image
            cv::Mat roiImage = fullImage(roiRect);

            // 4. Convert the ROI to grayscale
            cv::Mat grayRoi;
            cv::cvtColor(roiImage, grayRoi, cv::COLOR_BGRA2GRAY);

            // 5. Apply the Canny edge detection algorithm on the grayscale ROI
            cv::Mat cannyEdges;
            cv::Canny(grayRoi, cannyEdges, threshold1, threshold2);

            // 7. Copy the result to the output buffer.
            // The output buffer is expected to be the size of the ROI.
            size_t dataSize = cannyEdges.total() * cannyEdges.elemSize();
            std::cout << "  Copying " << dataSize << " bytes to output buffer" << std::endl;
            std::cout << "  Canny result size: " << cannyEdges.cols << "x" << cannyEdges.rows << std::endl;

            memcpy(outputImageData, cannyEdges.data, dataSize);

            std::cout << "ProcessImage_Canny completed successfully" << std::endl;
            return 0; // Success
        }
        catch (const cv::Exception& e) {
            std::cout << "OpenCV exception: " << e.what() << std::endl;
            return -1;
        }
        catch (const std::exception& e) {
            std::cout << "Standard exception: " << e.what() << std::endl;
            return -1;
        }
        catch (...) {
            std::cout << "Unknown exception occurred" << std::endl;
            return -1;
        }
    }

    // Forward declaration for the helper function
    void computeNormalsPCA(const std::vector<cv::Point>& curve, std::vector<cv::Point2f>& normals, int neighborhood);

    API int AnalyzeParallelism(
        const char* imagePath,
        int roiX,
        int roiY,
        int roiWidth,
        int roiHeight,
        int thresholdStart,
        int thresholdEnd,
        int thresholdStep,
        ProgressCallback callback,
        unsigned char* outImageBuffer,
        int outImageBufferSize
    ) {
        ParallelismResult result = { false, 0.0f };
        try {
            // 0. 加载图像
            cv::Mat image = cv::imread(imagePath, cv::IMREAD_COLOR);
            if (image.empty()) {
                std::cerr << "Error: Could not open or find the image at " << imagePath << std::endl;
                if (callback) callback(-1); // 发送错误信号
                return -3; // Indicate image load error
            }

            // 创建一个彩色副本用于绘制
            cv::Mat visImage = image.clone();

            // 1. 转换为灰度图
            cv::Mat grayImage;
            cv::cvtColor(image, grayImage, cv::COLOR_BGR2GRAY);

            // 2. 定义ROI并提取
            cv::Rect roiRect(roiX, roiY, roiWidth, roiHeight);
            roiRect &= cv::Rect(0, 0, grayImage.cols, grayImage.rows);
            if (roiRect.width <= 0 || roiRect.height <= 0) {
                std::cerr << "Error: Invalid ROI." << std::endl;
                if (callback) callback(-1); // 发送错误信号
                return -4; // Indicate invalid ROI
            }
            cv::Mat roi = grayImage(roiRect);

            // --- 阶段一：多阈值扫描与曲线提取 ---

            // 1.1 滤波去噪 (双边滤波)
            cv::Mat blurred;
            cv::bilateralFilter(roi, blurred, 9, 75, 75);

            // 1.2 创建主点集以融合所有轮廓点
            std::vector<cv::Point> masterPointSet;
            std::map<std::pair<int, int>, int> pointFrequency; // 统计点的出现频率

            // 1.3 实现带进度的阈值扫描
            int range = thresholdEnd - thresholdStart;
            if (range < 0) range = 0;

            for (int threshold = thresholdStart; threshold <= thresholdEnd; threshold += thresholdStep) {
                // a. 二值化
                cv::Mat binary;
                cv::threshold(blurred, binary, threshold, 255, cv::THRESH_BINARY);

                // b. 轮廓提取
                std::vector<std::vector<cv::Point>> contours;
                cv::findContours(binary, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);

                // c. 筛选和融合轮廓点
                std::cout << "Threshold " << threshold << ": Found " << contours.size() << " contours" << std::endl;

                for (const auto& contour : contours) {
                    // 筛选条件：轮廓长度和面积
                    double contourLength = cv::arcLength(contour, false);
                    double contourArea = cv::contourArea(contour);

                    // 放宽筛选条件：长度>5像素，面积>5像素²
                    if (contourLength > 5.0 && contourArea > 5.0) {
                        // 对轮廓进行近似，减少点数但保持形状
                        std::vector<cv::Point> approxContour;
                        cv::approxPolyDP(contour, approxContour, 1.0, false); // 减少近似程度

                        std::cout << "  Valid contour: length=" << contourLength
                                  << ", area=" << contourArea
                                  << ", points=" << approxContour.size() << std::endl;

                        // 统计点的频率
                        for (const auto& point : approxContour) {
                            std::pair<int, int> key = {point.x, point.y};
                            pointFrequency[key]++;
                        }
                    }
                }

                // d. 报告进度
                if (callback) {
                    int progress = 0;
                    if (range > 0) {
                        progress = (threshold - thresholdStart) * 50 / range; // 前50%进度用于扫描
                    }
                    // 确保最后一次循环能达到50%
                    if (threshold >= thresholdEnd) {
                        progress = 50;
                    }
                    callback(progress > 50 ? 50 : progress);
                }
            }

            // 1.4 基于频率筛选点，只保留出现频率较高的点
            int minFrequency = std::max(1, (thresholdEnd - thresholdStart) / (thresholdStep * 3)); // 至少出现1/3次
            for (const auto& pair : pointFrequency) {
                if (pair.second >= minFrequency) {
                    masterPointSet.push_back(cv::Point(pair.first.first, pair.first.second));
                }
            }

            if (callback) callback(55); // 点筛选完成

            if (masterPointSet.empty()) {
                std::cerr << "Error: No points found after multi-threshold scan." << std::endl;
                return -5; // Indicate no points found
            }

            // 1.5 曲线连续性优化
            // 去除重复点
            std::sort(masterPointSet.begin(), masterPointSet.end(), [](const cv::Point& a, const cv::Point& b) {
                return a.x < b.x || (a.x == b.x && a.y < b.y);
            });
            masterPointSet.erase(std::unique(masterPointSet.begin(), masterPointSet.end()), masterPointSet.end());

            // 按X坐标排序以便后续处理
            std::sort(masterPointSet.begin(), masterPointSet.end(), [](const cv::Point& a, const cv::Point& b) {
                return a.x < b.x;
            });

            // 曲线平滑：使用移动平均
            std::vector<cv::Point> smoothedPoints;
            int windowSize = 5; // 平滑窗口大小
            for (size_t i = 0; i < masterPointSet.size(); ++i) {
                int startIdx = std::max(0, static_cast<int>(i) - windowSize / 2);
                int endIdx = std::min(static_cast<int>(masterPointSet.size()) - 1, static_cast<int>(i) + windowSize / 2);

                int sumX = 0, sumY = 0, count = 0;
                for (int j = startIdx; j <= endIdx; ++j) {
                    sumX += masterPointSet[j].x;
                    sumY += masterPointSet[j].y;
                    count++;
                }

                if (count > 0) {
                    smoothedPoints.push_back(cv::Point(sumX / count, sumY / count));
                }
            }

            // 连接断裂的曲线段：填补X坐标间隙
            std::vector<cv::Point> connectedPoints;
            if (!smoothedPoints.empty()) {
                connectedPoints.push_back(smoothedPoints[0]);

                for (size_t i = 1; i < smoothedPoints.size(); ++i) {
                    cv::Point prev = smoothedPoints[i - 1];
                    cv::Point curr = smoothedPoints[i];

                    // 如果X坐标间隙大于3像素，进行插值
                    if (curr.x - prev.x > 3) {
                        int steps = curr.x - prev.x;
                        for (int step = 1; step < steps; ++step) {
                            int interpX = prev.x + step;
                            int interpY = prev.y + (curr.y - prev.y) * step / steps;
                            connectedPoints.push_back(cv::Point(interpX, interpY));
                        }
                    }
                    connectedPoints.push_back(curr);
                }
            }

            masterPointSet = connectedPoints;

            if (callback) callback(65); // 曲线优化完成

            // 1.6 改进的曲线分离算法
            std::vector<cv::Point> curve_A, curve_B; // 上曲线和下曲线

            // 方法1：基于X坐标分组，每个X坐标找到最上和最下的点
            std::map<int, std::vector<cv::Point>> pointsByX;
            for (const auto& point : masterPointSet) {
                pointsByX[point.x].push_back(point);
            }

            // 对每个X坐标的点按Y排序，取最上和最下的点
            for (auto& pair : pointsByX) {
                std::vector<cv::Point>& points = pair.second;
                if (points.size() == 1) {
                    // 只有一个点，需要判断它属于上曲线还是下曲线
                    // 暂时都加入，后续会进一步分离
                    curve_A.push_back(points[0]);
                    curve_B.push_back(points[0]);
                } else {
                    // 多个点，按Y排序
                    std::sort(points.begin(), points.end(), [](const cv::Point& a, const cv::Point& b) {
                        return a.y < b.y;
                    });

                    // 取最上面的点作为上曲线
                    curve_A.push_back(points[0]);
                    // 取最下面的点作为下曲线
                    curve_B.push_back(points.back());

                    // 如果有中间点，根据距离分配给上下曲线
                    for (size_t i = 1; i < points.size() - 1; ++i) {
                        int distToTop = points[i].y - points[0].y;
                        int distToBottom = points.back().y - points[i].y;

                        if (distToTop < distToBottom) {
                            curve_A.push_back(points[i]);
                        } else {
                            curve_B.push_back(points[i]);
                        }
                    }
                }
            }

            // 按X坐标排序两条曲线
            std::sort(curve_A.begin(), curve_A.end(), [](const cv::Point& a, const cv::Point& b) {
                return a.x < b.x;
            });
            std::sort(curve_B.begin(), curve_B.end(), [](const cv::Point& a, const cv::Point& b) {
                return a.x < b.x;
            });

            // 进一步优化：去除明显的异常点
            auto removeOutliers = [](std::vector<cv::Point>& curve) {
                if (curve.size() < 3) return;

                std::vector<cv::Point> filtered;
                filtered.push_back(curve[0]);

                for (size_t i = 1; i < curve.size() - 1; ++i) {
                    cv::Point prev = curve[i - 1];
                    cv::Point curr = curve[i];
                    cv::Point next = curve[i + 1];

                    // 计算当前点与前后点的Y坐标差异
                    int diffPrev = std::abs(curr.y - prev.y);
                    int diffNext = std::abs(curr.y - next.y);

                    // 如果差异不是太大，保留这个点
                    if (diffPrev < 20 && diffNext < 20) {
                        filtered.push_back(curr);
                    }
                }

                if (!curve.empty()) {
                    filtered.push_back(curve.back());
                }

                curve = filtered;
            };

            removeOutliers(curve_A);
            removeOutliers(curve_B);

            if (curve_A.empty() || curve_B.empty()) {
                std::cerr << "Error: Could not separate curves properly." << std::endl;
                return -6; // Indicate curve separation error
            }

            if (callback) callback(75); // 曲线分离完成

            // 1.7 曲线拟合和平滑
            auto fitAndSmoothCurve = [](std::vector<cv::Point>& curve) {
                if (curve.size() < 3) return;

                // 使用多项式拟合来平滑曲线
                std::vector<cv::Point> fittedCurve;

                // 简单的移动平均平滑
                int smoothWindow = std::min(5, static_cast<int>(curve.size()) / 3);
                for (size_t i = 0; i < curve.size(); ++i) {
                    int startIdx = std::max(0, static_cast<int>(i) - smoothWindow / 2);
                    int endIdx = std::min(static_cast<int>(curve.size()) - 1, static_cast<int>(i) + smoothWindow / 2);

                    int sumX = 0, sumY = 0, count = 0;
                    for (int j = startIdx; j <= endIdx; ++j) {
                        sumX += curve[j].x;
                        sumY += curve[j].y;
                        count++;
                    }

                    if (count > 0) {
                        fittedCurve.push_back(cv::Point(sumX / count, sumY / count));
                    }
                }

                curve = fittedCurve;
            };

            fitAndSmoothCurve(curve_A);
            fitAndSmoothCurve(curve_B);

            if (curve_A.empty() || curve_B.empty()) {
                std::cerr << "Error: One or both curves are empty after processing." << std::endl;
                return -7; // Indicate empty curves
            }

            if (callback) callback(80); // 曲线拟合完成

            // --- 创建原图ROI区域图像（带曲线，用于左下角显示） ---
            cv::Mat roiRawImage = image(roiRect).clone(); // 从原图提取ROI区域

            // 在ROI原图上绘制曲线（不需要偏移，因为坐标已经是相对于ROI的）
            if (!curve_A.empty()) {
                cv::polylines(roiRawImage, curve_A, false, cv::Scalar(0, 255, 0), 2); // 绿色上曲线

                // 绘制曲线点
                for (const auto& point : curve_A) {
                    cv::circle(roiRawImage, point, 2, cv::Scalar(0, 255, 0), -1);
                }
            }

            if (!curve_B.empty()) {
                cv::polylines(roiRawImage, curve_B, false, cv::Scalar(255, 0, 0), 2); // 蓝色下曲线

                // 绘制曲线点
                for (const auto& point : curve_B) {
                    cv::circle(roiRawImage, point, 2, cv::Scalar(255, 0, 0), -1);
                }
            }

            // 添加连接线显示对应关系
            if (!curve_A.empty() && !curve_B.empty()) {
                int step = std::max(1, static_cast<int>(std::min(curve_A.size(), curve_B.size())) / 8);
                for (size_t i = 0; i < std::min(curve_A.size(), curve_B.size()); i += step) {
                    cv::line(roiRawImage, curve_A[i], curve_B[i], cv::Scalar(200, 200, 200), 1);
                }
            }

            // 添加文字信息
            cv::putText(roiRawImage, "ROI Raw + Curves", cv::Point(10, 20),
                       cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(255, 255, 255), 2);

            std::string curveInfo = "Upper: " + std::to_string(curve_A.size()) + " pts";
            cv::putText(roiRawImage, curveInfo, cv::Point(10, 40),
                       cv::FONT_HERSHEY_SIMPLEX, 0.4, cv::Scalar(0, 255, 0), 1);

            curveInfo = "Lower: " + std::to_string(curve_B.size()) + " pts";
            cv::putText(roiRawImage, curveInfo, cv::Point(10, 55),
                       cv::FONT_HERSHEY_SIMPLEX, 0.4, cv::Scalar(255, 0, 0), 1);

            // --- 创建ROI分析图（右上角显示） ---
            cv::Mat roiAnalysisImage;
            roi.copyTo(roiRawImage); // 复制ROI区域

            // 如果是灰度图，转换为彩色以便绘制彩色曲线
            if (roiAnalysisImage.channels() == 1) {
                cv::cvtColor(roiRawImage, roiRawImage, cv::COLOR_GRAY2BGR);
            }

            // 在ROI分析图上绘制曲线（不需要偏移，因为坐标已经是相对于ROI的）
            if (!curve_A.empty()) {
                cv::polylines(roiRawImage, curve_A, false, cv::Scalar(0, 255, 0), 2); // 绿色上曲线，更粗

                // 绘制曲线点
                for (const auto& point : curve_A) {
                    cv::circle(roiRawImage, point, 2, cv::Scalar(0, 255, 0), -1);
                }
            }

            if (!curve_B.empty()) {
                cv::polylines(roiRawImage, curve_B, false, cv::Scalar(255, 0, 0), 2); // 蓝色下曲线，更粗

                // 绘制曲线点
                for (const auto& point : curve_B) {
                    cv::circle(roiRawImage, point, 2, cv::Scalar(255, 0, 0), -1);
                }
            }

            // 添加连接线显示对应关系
            if (!curve_A.empty() && !curve_B.empty()) {
                int step = std::max(1, static_cast<int>(std::min(curve_A.size(), curve_B.size())) / 6);
                for (size_t i = 0; i < std::min(curve_A.size(), curve_B.size()); i += step) {
                    cv::line(roiRawImage, curve_A[i], curve_B[i], cv::Scalar(200, 200, 200), 2);
                }
            }

            // 添加文字信息
            cv::putText(roiRawImage, "ROI Analysis", cv::Point(10, 20),
                       cv::FONT_HERSHEY_SIMPLEX, 0.8, cv::Scalar(255, 255, 255), 2);

            curveInfo = "Upper: " + std::to_string(curve_A.size()) + " pts";
            cv::putText(roiRawImage, curveInfo, cv::Point(10, 45),
                       cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 255, 0), 2);

            curveInfo = "Lower: " + std::to_string(curve_B.size()) + " pts";
            cv::putText(roiRawImage, curveInfo, cv::Point(10, 65),
                       cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(255, 0, 0), 2);

            // 保存分析图
            std::string analysisImagePath = std::string(imagePath);
            size_t lastDot = analysisImagePath.find_last_of('.');
            if (lastDot != std::string::npos) {
                analysisImagePath = analysisImagePath.substr(0, lastDot) + "_analysis.jpg";
            } else {
                analysisImagePath += "_analysis.jpg";
            }
            cv::imwrite(analysisImagePath, roiAnalysisImage);

            // 保存ROI原图
            std::string roiRawImagePath = std::string(imagePath);
            if (lastDot != std::string::npos) {
                roiRawImagePath = roiRawImagePath.substr(0, lastDot) + "_roi_raw.jpg";
            } else {
                roiRawImagePath += "_roi_raw.jpg";
            }
            cv::imwrite(roiRawImagePath, roiRawImage);

            std::cout << "Analysis image saved to: " << analysisImagePath << std::endl;
            std::cout << "ROI raw image saved to: " << roiRawImagePath << std::endl;


            if (callback) callback(90); // 可视化完成

            // 跳过平行性计算，直接返回图像

            // --- 返回ROI分析图（用于Processed Image显示） ---
            std::vector<unsigned char> buf;
            // 使用.jpg格式以获得较好的压缩率，返回ROI分析图
            if (cv::imencode(".jpg", roiAnalysisImage, buf, {cv::IMWRITE_JPEG_QUALITY, 90})) {
                if (buf.size() <= static_cast<size_t>(outImageBufferSize)) {
                    memcpy(outImageBuffer, buf.data(), buf.size());
                    return static_cast<int>(buf.size()); // 返回写入的字节数
                } else {
                    return -1; // 缓冲区太小
                }
            } else {
                return -2; // 编码失败
            }
        }
        catch (const cv::Exception& e) {
            std::cerr << "OpenCV exception in AnalyzeParallelism: " << e.what() << std::endl;
            return -98; // Indicate OpenCV exception
        }
        catch (const std::exception& e) {
            std::cerr << "Standard exception in AnalyzeParallelism: " << e.what() << std::endl;
            return -99; // Indicate standard exception
        }
        catch (...) {
            std::cerr << "Unknown exception occurred in AnalyzeParallelism" << std::endl;
            return -100; // Indicate unknown exception
        }
    }

    // 根据规范重写的函数
    API int AnalyzeCurvesFromProcessedImage(
        unsigned char* processedImageData,
        unsigned char* originalRoiImageData, // 新增参数
        int width,
        int height,
        unsigned char* outImageBuffer,
        int outImageBufferSize
    ) {
        // 健壮性：顶层 try-catch 块
        try {
            // 健壮性：检查空指针
            if (!processedImageData || !originalRoiImageData || !outImageBuffer) {
                return -5; // 自定义错误码：空指针输入
            }

            // 1. 将输入数据包装为 cv::Mat
            cv::Mat processedImage(height, width, CV_8UC1, processedImageData);
            // 将原始ROI数据包装为cv::Mat (BGRA格式)
            cv::Mat visImage(height, width, CV_8UC4, originalRoiImageData);

            // 2. 轮廓处理 (在分析图像上进行)
            std::vector<std::vector<cv::Point>> contours;
            cv::findContours(processedImage, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);

            // 根据点数过滤噪声轮廓
            contours.erase(std::remove_if(contours.begin(), contours.end(),
                [](const std::vector<cv::Point>& c) {
                    return c.size() <= 20;
                }), contours.end());

            if (contours.size() < 2) {
                return -4; // 错误码：轮廓不足
            }

            // 按点数对轮廓降序排序
            std::sort(contours.begin(), contours.end(),
                [](const std::vector<cv::Point>& a, const std::vector<cv::Point>& b) {
                    return a.size() > b.size();
                });

            // 选取最大的两个轮廓
            const std::vector<cv::Point>& contour1 = contours[0];
            const std::vector<cv::Point>& contour2 = contours[1];

            // 注意：现在 visImage 是从 originalRoiImageData 创建的，已经是彩色背景

            // 3. 对两个主轮廓进行椭圆弧拟合和绘制
            auto fitAndDrawArc = [&](const std::vector<cv::Point>& contour, const cv::Scalar& color) {
                if (contour.size() < 5) return; // fitEllipse 需要至少5个点

                cv::RotatedRect fittedEllipse = cv::fitEllipse(contour);

                // 计算精确的起止角度
                float minAngle = 360.0f, maxAngle = -360.0f;
                std::vector<float> angles;
                
                float ellipseAngleRad = fittedEllipse.angle * (float)CV_PI / 180.0f;
                float cosA = cos(ellipseAngleRad);
                float sinA = sin(ellipseAngleRad);

                for (const auto& pt : contour) {
                    // 1. 平移到椭圆中心
                    cv::Point2f centeredPt(pt.x - fittedEllipse.center.x, pt.y - fittedEllipse.center.y);
                    
                    // 2. 反向旋转点以对齐椭圆坐标轴
                    float x_rotated = centeredPt.x * cosA + centeredPt.y * sinA;
                    float y_rotated = -centeredPt.x * sinA + centeredPt.y * cosA;

                    // 3. 计算角度
                    float angle = atan2(y_rotated, x_rotated) * 180.0f / (float)CV_PI;
                    angles.push_back(angle);
                    if (angle < minAngle) minAngle = angle;
                    if (angle > maxAngle) maxAngle = angle;
                }

                // 修正跨越 PI/-PI 边界的角度范围
                if (maxAngle - minAngle > 180.0f && maxAngle > 90.0f && minAngle < -90.0f) {
                    minAngle = 360.0f;
                    maxAngle = -360.0f;
                    for (float& angle : angles) {
                        if (angle < 0) angle += 360.0f;
                        if (angle < minAngle) minAngle = angle;
                        if (angle > maxAngle) maxAngle = angle;
                    }
                }
                
                // 使用精确角度绘制椭圆弧
                cv::ellipse(visImage, fittedEllipse.center, fittedEllipse.size / 2.0f, fittedEllipse.angle, minAngle, maxAngle, color, 3, cv::LINE_AA);
            };

            fitAndDrawArc(contour1, cv::Scalar(0, 255, 0)); // 绿色
            fitAndDrawArc(contour2, cv::Scalar(255, 0, 0)); // 蓝色

            // 4. 内存管理：编码图像到内存缓冲区
            std::vector<unsigned char> buf;
            // 使用 .jpg 格式进行编码
            if (!cv::imencode(".jpg", visImage, buf, { cv::IMWRITE_JPEG_QUALITY, 95 })) {
                return -2; // 错误码：编码失败
            }

            // 健壮性：检查缓冲区大小
            if (buf.size() > static_cast<size_t>(outImageBufferSize)) {
                return -1; // 错误码：缓冲区太小
            }

            // 内存管理：将数据复制到调用者分配的缓冲区
            memcpy(outImageBuffer, buf.data(), buf.size());

            // 成功：返回写入的字节数
            return static_cast<int>(buf.size());
        }
        catch (const cv::Exception& e) {
            // 健壮性：捕获 OpenCV 异常
            std::cerr << "OpenCV Exception: " << e.what() << std::endl;
            return -3; // 错误码：OpenCV 异常
        }
        catch (const std::exception& e) {
            // 健壮性：捕获标准异常
            std::cerr << "Standard Exception: " << e.what() << std::endl;
            return -99; // 错误码：其他异常
        }
        catch (...) {
            // 健壮性：捕获未知异常
            std::cerr << "Unknown exception occurred." << std::endl;
            return -100; // 错误码：未知异常
        }
    }

    // 辅助函数：使用PCA为曲线上的每个点计算法向量
    void computeNormalsPCA(const std::vector<cv::Point>& curve, std::vector<cv::Point2f>& normals, int neighborhood) {
        normals.clear();
        if (curve.size() < 2 * neighborhood + 1) {
            // 点数太少，无法计算
            return;
        }

        for (size_t i = 0; i < curve.size(); ++i) {
            std::vector<cv::Point> neighborhoodPoints;
            for (int j = -neighborhood; j <= neighborhood; ++j) {
                // 处理边界情况
                int index = static_cast<int>(i) + j;
                if (index < 0) {
                    index = 0;
                }
                if (index >= static_cast<int>(curve.size())) {
                    index = static_cast<int>(curve.size()) - 1;
                }
                neighborhoodPoints.push_back(curve[index]);
            }

            // 将邻域点转换为Mat
            cv::Mat data(neighborhoodPoints.size(), 2, CV_32F);
            for (size_t k = 0; k < neighborhoodPoints.size(); ++k) {
                data.at<float>(k, 0) = static_cast<float>(neighborhoodPoints[k].x);
                data.at<float>(k, 1) = static_cast<float>(neighborhoodPoints[k].y);
            }

            // 执行PCA
            cv::PCA pca(data, cv::Mat(), cv::PCA::DATA_AS_ROW);

            // 法向量是与最小特征值关联的特征向量
            cv::Point2f normal(pca.eigenvectors.at<float>(1, 0), pca.eigenvectors.at<float>(1, 1));

            // 统一法向量方向（Y分量为正，指向图像下方）
            if (normal.y < 0) {
                normal = -normal;
            }
            
            // L2归一化
            double norm_val = cv::norm(normal);
            if (norm_val > 1e-6) { // Avoid division by zero
                normal /= norm_val;
            }

            normals.push_back(normal);
        }
    }
}