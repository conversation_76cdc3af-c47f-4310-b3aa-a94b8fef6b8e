﻿<Window x:Class="ImageAnalysisTool.WPF.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ImageAnalysisTool.WPF.Views"
        xmlns:viewModels="clr-namespace:ImageAnalysisTool.WPF.ViewModels"
        mc:Ignorable="d"
        Title="Image Analysis Tool" Height="600" Width="1000">
    
    <Window.DataContext>
        <viewModels:MainViewModel/>
    </Window.DataContext>

    <Window.Resources>
        <local:InvertBooleanConverter x:Key="InvertBooleanConverter"/>
    </Window.Resources>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Control Panel -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
            <Button Content="Load Image" Command="{Binding LoadImageCommand}" Width="120" Height="30" Margin="5"/>

            <StackPanel Orientation="Vertical" Margin="20,0,0,0">
                <TextBlock Text="Threshold 1"/>
                <Slider Minimum="0" Maximum="255" Value="{Binding Threshold1, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Width="200"/>
                <TextBlock Text="{Binding Threshold1, StringFormat={}{0:F0}}" HorizontalAlignment="Center"/>
            </StackPanel>

            <StackPanel Orientation="Vertical" Margin="20,0,0,0">
                <TextBlock Text="Threshold 2"/>
                <Slider Minimum="0" Maximum="255" Value="{Binding Threshold2, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Width="200"/>
                <TextBlock Text="{Binding Threshold2, StringFormat={}{0:F0}}" HorizontalAlignment="Center"/>
            </StackPanel>


            <Button Content="Maximize ROI" Command="{Binding MaximizeRoiCommand}" Width="120" Height="30" Margin="20,0,0,0" VerticalAlignment="Center"/>

            <StackPanel Orientation="Vertical" Margin="20,0,0,0" VerticalAlignment="Center">
                <TextBlock Text="Scan Parameters" FontWeight="Bold"/>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="Start:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBox Text="{Binding ThresholdStart, UpdateSourceTrigger=PropertyChanged}" Width="40"/>
                    <TextBlock Text="End:" VerticalAlignment="Center" Margin="10,0,5,0"/>
                    <TextBox Text="{Binding ThresholdEnd, UpdateSourceTrigger=PropertyChanged}" Width="40"/>
                    <TextBlock Text="Step:" VerticalAlignment="Center" Margin="10,0,5,0"/>
                    <TextBox Text="{Binding ThresholdStep, UpdateSourceTrigger=PropertyChanged}" Width="40"/>
                </StackPanel>
            </StackPanel>

            <StackPanel Orientation="Vertical" Margin="20,0,0,0" VerticalAlignment="Center">
                <Button Content="Analyze Parallelism" Command="{Binding AnalyzeParallelismCommand}" IsEnabled="{Binding IsAnalyzing, Converter={StaticResource InvertBooleanConverter}}" Width="150" Height="30"/>
                <Button Content="Save Analysis Images" Command="{Binding SaveAnalysisImagesCommand}" IsEnabled="{Binding CanSaveAnalysis}" Width="150" Height="30" Margin="0,5,0,0"/>
                <ProgressBar Value="{Binding AnalysisProgress}" Minimum="0" Maximum="100" Height="10" Margin="0,5,0,0"/>
                <TextBlock Text="{Binding ParallelismResultText}" Margin="0,5,0,0" HorizontalAlignment="Center" MinHeight="30"/>
            </StackPanel>

        </StackPanel>

        <!-- Image Display -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Border Grid.Column="0" BorderBrush="Gray" BorderThickness="1" Margin="5">
                <Viewbox Stretch="Uniform">
                    <Canvas Width="{Binding OriginalImage.PixelWidth}"
                            Height="{Binding OriginalImage.PixelHeight}"
                            MouseDown="ImageCanvas_MouseDown"
                            MouseMove="ImageCanvas_MouseMove"
                            MouseUp="ImageCanvas_MouseUp"
                            Background="Transparent">

                        <Image Source="{Binding OriginalImage}" Stretch="Fill"/>

                        <Rectangle x:Name="RoiRectangle"
                                   Stroke="Orange"
                                   StrokeThickness="3"
                                   Fill="Transparent"
                                   Canvas.Left="{Binding RoiRect.X}"
                                   Canvas.Top="{Binding RoiRect.Y}"
                                   Width="{Binding RoiRect.Width}"
                                   Height="{Binding RoiRect.Height}"/>
                    </Canvas>
                </Viewbox>
            </Border>
            <TextBlock Grid.Column="0" Text="Original Image" VerticalAlignment="Top" HorizontalAlignment="Center" Margin="10" FontWeight="Bold"/>


            <!-- Right Column: Two rows for Processed Image and ROI Raw -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Processed Image (Top) -->
                <Border Grid.Row="0" BorderBrush="Gray" BorderThickness="1" Margin="5">
                    <Viewbox>
                        <Image Source="{Binding ProcessedImage}" Stretch="Uniform"/>
                    </Viewbox>
                </Border>
                <TextBlock Grid.Row="0" Text="Processed Image" VerticalAlignment="Top" HorizontalAlignment="Center" Margin="10" FontWeight="Bold"/>

                <!-- ROI Raw (Bottom) -->
                <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1" Margin="5">
                    <Viewbox>
                        <Image Source="{Binding RoiRawImage}" Stretch="Uniform"/>
                    </Viewbox>
                </Border>
                <TextBlock Grid.Row="1" Text="ROI Raw" VerticalAlignment="Top" HorizontalAlignment="Center" Margin="10" FontWeight="Bold"/>
            </Grid>
        </Grid>
    </Grid>
</Window>
