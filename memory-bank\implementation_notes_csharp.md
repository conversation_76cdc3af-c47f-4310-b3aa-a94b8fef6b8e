# C# Implementation Notes

This document records key decisions and technical details related to the C# WPF application implementation.

---

### Decision (C#)
[2025-07-09 21:04:00] - Standardize Bitmap/Image Data Handling for P/Invoke

**Rationale:**
To ensure stable and performant data exchange between the C# application and the C++ DLL, a standardized approach for handling image data is crucial. The chosen method involves converting all loaded images to a consistent pixel format (`Bgra32`) before processing and carefully managing the conversion of the processed data back into a displayable format.

**Details:**
1.  **Input Image Conversion:**
    *   When an image is loaded via the `LoadImage` method in `MainViewModel.cs`, it is immediately converted to a `FormatConvertedBitmap` with `PixelFormats.Bgra32`.
    *   **Reasoning:** This guarantees that the byte array sent to the C++ DLL always has a predictable 4-bytes-per-pixel structure (Blue, Green, Red, Alpha), simplifying the marshalling and C++-side processing. It avoids complexities from handling various source image formats (e.g., indexed color, CMYK, different RGB orderings).
    *   Reference: `MainViewModel.cs`, `LoadImage()` method.

2.  **Output Image Conversion:**
    *   The C++ `ProcessImage_Canny` function returns a single-channel, 8-bit grayscale image (`byte[]`).
    *   This byte array is converted into a `BitmapSource` using `BitmapSource.Create()`.
    *   The `PixelFormats.Gray8` format is specified, which directly maps the single-channel data to a grayscale image.
    *   Reference: `MainViewModel.cs`, `UpdateProcessedImage()` method.

3.  **Performance Optimization (`.Freeze()`):**
    *   The `ProcessedImage` `BitmapSource` has its `.Freeze()` method called immediately after creation.
    *   **Reasoning:** Freezing the object makes it immutable and allows it to be accessed from different threads without requiring synchronization. Since the `ProcessedImage` is generated in the ViewModel and displayed by the UI thread, this is a critical performance optimization that prevents potential cross-thread access exceptions and improves UI responsiveness.

4.  **Error Handling:**
    *   A `try-catch` block is wrapped around the entire `UpdateProcessedImage` logic.
    *   A specific `catch` for `DllNotFoundException` provides a user-friendly error message if the C++ DLL is missing, which is a common issue in P/Invoke scenarios.
    *   A general `catch` for other exceptions prevents the application from crashing due to unexpected errors during processing.
    *   Reference: `MainViewModel.cs`, `UpdateProcessedImage()` method.

---

### Decision (C#)
[2025-07-09 22:24:00] - Implement ROI Mouse Interaction in View's Code-Behind

**Rationale:**
Following the architectural decision outlined in `architecture_overview.md` and `decisionLog.md`, the logic for handling direct user manipulation of the ROI (Region of Interest) rectangle is placed in the `MainWindow.xaml.cs` file. This adheres strictly to the MVVM pattern.

**Details:**
1.  **Responsibility:** The View (`MainWindow.xaml.cs`) is responsible for capturing and interpreting raw mouse events (`MouseDown`, `MouseMove`, `MouseUp`) that occur on the image `Canvas`.
2.  **State Management:** The code-behind manages the temporary state required for the drag operation, such as the starting point (`_startPoint`) and whether a drag is in progress (`_isDragging`).
3.  **Coordinate Handling:** It handles the logic of calculating the rectangle's dimensions based on the mouse position and, crucially, clamps these values to the boundaries of the original image to prevent invalid ROI definitions.
4.  **ViewModel Update:** Upon modification, the code-behind directly updates the `RoiRect` property on the `MainViewModel`. The data binding mechanism ensures the ViewModel is notified, which in turn triggers the `UpdateProcessedImage` method.
5.  **Decoupling:** This approach keeps the `MainViewModel` clean of any UI-specific event handling logic. The ViewModel's only responsibility is to own the `RoiRect` state and react when it changes, making it more portable and testable.
6.  **Reference:** `csharp/ImageAnalysisTool.WPF/Views/MainWindow.xaml.cs`

---

### Decision (C#)
[2025-07-10 09:13:00] - Integrate C++ Parallelism Analysis Feature into WPF Frontend

**Rationale:**
To expose the new `AnalyzeParallelism` C++ function to the user, the C# frontend required updates to its P/Invoke definitions, ViewModel logic, and UI. The implementation follows existing patterns to ensure consistency and maintainability.

**Details:**

1.  **P/Invoke Definition (`NativeMethods.cs`):**
    *   A new struct `ParallelismResult` was defined with a `[StructLayout(LayoutKind.Sequential)]` attribute to ensure its memory layout matches the C++ version precisely.
    *   The `AnalyzeParallelism` function was declared using `[DllImport]`.
    *   The `imagePath` parameter is marshaled as `UnmanagedType.LPStr` to correctly pass the .NET `string` to the C-style `const char*` expected by the DLL.
    *   **Reference:** `csharp/ImageAnalysisTool.WPF/Interop/NativeMethods.cs`

2.  **ViewModel Integration (`MainViewModel.cs`):**
    *   A new property `_originalImagePath` was added to store the file path of the loaded image, as this is required by the `AnalyzeParallelism` function.
    *   A new `ICommand` named `AnalyzeParallelismCommand` was created to be bound to the UI button.
    *   A corresponding `AnalyzeParallelism()` method calls the native function, passing the stored image path and the current ROI.
    *   A `ParallelismResultText` property was added to store the formatted string returned by the analysis for display in the UI.
    *   **Reference:** `csharp/ImageAnalysisTool.WPF/ViewModels/MainViewModel.cs`

3.  **UI Implementation (`MainWindow.xaml`):**
    *   A new `Button` was added to the control panel and bound to the `AnalyzeParallelismCommand`.
    *   A `TextBlock` was added to display the `ParallelismResultText` from the ViewModel, providing clear feedback to the user.
    *   **Reference:** `csharp/ImageAnalysisTool.WPF/Views/MainWindow.xaml`

---
### Decision (Code)
[2025-07-10 09:50:00] - Synchronize C# frontend with C++ backend update.

**Rationale:**
The C++ `AnalyzeParallelism` function was refactored to use an internal multi-threshold scanning technique, removing the need for an external `grayscaleThreshold` parameter. The C# frontend must be updated to match this new interface.

**Details:**
1.  **P/Invoke Signature Update:**
    *   The `grayscaleThreshold` parameter was removed from the `ProcessImage_Canny` function's P/Invoke signature in `NativeMethods.cs`. The `AnalyzeParallelism` signature was already correct.
    *   File: `csharp/ImageAnalysisTool.WPF/Interop/NativeMethods.cs`

2.  **ViewModel Cleanup:**
    *   The `GrayscaleThreshold` property and all associated logic were removed from `MainViewModel.cs`.
    *   The call to `NativeMethods.ProcessImage_Canny` within `UpdateProcessedImage` was updated to use the new, shorter signature.
    *   File: `csharp/ImageAnalysisTool.WPF/ViewModels/MainViewModel.cs`

3.  **UI Cleanup:**
    *   The `Slider` and `TextBlock` controls for the "Grayscale Threshold" were removed from the UI.
    *   File: `csharp/ImageAnalysisTool.WPF/Views/MainWindow.xaml`
---
### Decision (C#)
[2025-07-10 10:16:00] - Refactor Parallelism Analysis for Asynchronous Execution with Progress Reporting

**Rationale:**
The original `AnalyzeParallelism` function was synchronous, causing the UI to freeze during potentially long-running analysis operations. To enhance user experience, the feature was refactored to run asynchronously on a background thread, providing real-time progress feedback and keeping the UI responsive.

**Details:**

1.  **P/Invoke Signature Update (`NativeMethods.cs`):**
    *   A new delegate, `ProgressCallback`, was defined with the `[UnmanagedFunctionPointer(CallingConvention.Cdecl)]` attribute to match the function pointer type expected by the C++ DLL.
    *   The P/Invoke signature for `AnalyzeParallelism` was updated to include parameters for `thresholdStart`, `thresholdEnd`, `thresholdStep`, and the `ProgressCallback` delegate.
    *   **Reference:** `csharp/ImageAnalysisTool.WPF/Interop/NativeMethods.cs`

2.  **Asynchronous ViewModel Logic (`MainViewModel.cs`):**
    *   The `AnalyzeParallelism` method was converted to an `async void` method.
    *   A new boolean property, `IsAnalyzing`, was introduced to track the analysis state. This property is used to disable the analysis button during execution, preventing concurrent runs.
    *   The core native method call is now wrapped in `await Task.Run(...)`, ensuring the expensive C++ computation runs on a background thread pool thread, not the UI thread.
    *   A `finally` block is used to guarantee that `IsAnalyzing` is set back to `false` even if the analysis fails.
    *   **Reference:** `csharp/ImageAnalysisTool.WPF/ViewModels/MainViewModel.cs`

3.  **Thread-Safe Progress Reporting (`MainViewModel.cs`):**
    *   A `ProgressCallback` delegate instance is created, pointing to a new `UpdateProgress` method.
    *   The `UpdateProgress` method uses `Application.Current.Dispatcher.Invoke` to marshal the progress update from the background thread back to the UI thread. This is essential for safely updating data-bound properties like `AnalysisProgress`.
    *   A new integer property, `AnalysisProgress`, was added to the ViewModel to store the current progress percentage (0-100).
    *   **Reference:** `csharp/ImageAnalysisTool.WPF/ViewModels/MainViewModel.cs`

4.  **UI Enhancements (`MainWindow.xaml`):**
    *   Three `TextBox` controls were added, bound to new ViewModel properties (`ThresholdStart`, `ThresholdEnd`, `ThresholdStep`), allowing the user to customize the scan parameters.
    *   A `ProgressBar` was added, with its `Value` bound to the `AnalysisProgress` property to display real-time feedback.
    *   The `Analyze Parallelism` button's `IsEnabled` property is now bound to `IsAnalyzing` via a converter, providing immediate visual feedback.
    *   **Reference:** `csharp/ImageAnalysisTool.WPF/Views/MainWindow.xaml`

5.  **Value Converter (`MainWindow.xaml.cs`):**
    *   An `IValueConverter` named `InvertBooleanConverter` was implemented.
    *   This converter is used in the XAML binding to invert the `IsAnalyzing` boolean value for the button's `IsEnabled` property (i.e., the button is enabled when `IsAnalyzing` is `false`).
    *   The converter was defined in the view's code-behind and instantiated as a resource in the XAML.
    *   **Reference:** `csharp/ImageAnalysisTool.WPF/Views/MainWindow.xaml.cs` &amp; `MainWindow.xaml`