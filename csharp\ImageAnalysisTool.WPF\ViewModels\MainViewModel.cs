using System;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Threading;
using ImageAnalysisTool.WPF.Interop;
using Microsoft.Win32;

namespace ImageAnalysisTool.WPF.ViewModels
{
    public class MainViewModel : ViewModelBase
    {
        private DispatcherTimer? _updateTimer;
        private BitmapSource? _originalImage;
        private string? _originalImagePath;

        public BitmapSource? OriginalImage
        {
            get => _originalImage;
            set
            {
                if (SetField(ref _originalImage, value))
                {
                    _updateTimer?.Stop();
                    UpdateProcessedImage();
                    ParallelismResultText = string.Empty;
                }
            }
        }

        private BitmapSource? _processedImage;
        public BitmapSource? ProcessedImage
        {
            get => _processedImage;
            set => SetField(ref _processedImage, value);
        }

        private BitmapSource? _roiRawImage;
        public BitmapSource? RoiRawImage
        {
            get => _roiRawImage;
            set => SetField(ref _roiRawImage, value);
        }

        private double _threshold1 = 50;
        public double Threshold1
        {
            get => _threshold1;
            set
            {
                if (SetField(ref _threshold1, value))
                {
                    ScheduleUpdateProcessedImage();
                }
            }
        }

        private double _threshold2 = 150;
        public double Threshold2
        {
            get => _threshold2;
            set
            {
                if (SetField(ref _threshold2, value))
                {
                    ScheduleUpdateProcessedImage();
                }
            }
        }

        private Rect _roiRect;
        public Rect RoiRect
        {
            get => _roiRect;
            set
            {
                if (_roiRect == value) return;
                _roiRect = value;
                OnPropertyChanged();
                UpdateRoiRawImage(); // 更新ROI原图显示
                ScheduleUpdateProcessedImage();
            }
        }

        private string _parallelismResultText = string.Empty;
        public string ParallelismResultText
        {
            get => _parallelismResultText;
            set => SetField(ref _parallelismResultText, value);
        }

        private int _thresholdStart = 50;
        public int ThresholdStart
        {
            get => _thresholdStart;
            set => SetField(ref _thresholdStart, value);
        }

        private int _thresholdEnd = 200;
        public int ThresholdEnd
        {
            get => _thresholdEnd;
            set => SetField(ref _thresholdEnd, value);
        }

        private int _thresholdStep = 10;
        public int ThresholdStep
        {
            get => _thresholdStep;
            set => SetField(ref _thresholdStep, value);
        }

        private int _analysisProgress;
        public int AnalysisProgress
        {
            get => _analysisProgress;
            set => SetField(ref _analysisProgress, value);
        }

        private bool _isAnalyzing;
        public bool IsAnalyzing
        {
            get => _isAnalyzing;
            set
            {
                if (SetField(ref _isAnalyzing, value))
                {
                    // This ensures the command's CanExecute is re-evaluated
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        public ICommand LoadImageCommand { get; }
        public ICommand MaximizeRoiCommand { get; }
        public ICommand AnalyzeParallelismCommand { get; }
        public ICommand SaveAnalysisImagesCommand { get; }

        public MainViewModel()
        {
            LoadImageCommand = new RelayCommand(LoadImage);
            MaximizeRoiCommand = new RelayCommand(MaximizeRoi, () => OriginalImage != null);
            AnalyzeParallelismCommand = new RelayCommand(AnalyzeParallelism, CanAnalyze);
            SaveAnalysisImagesCommand = new RelayCommand(SaveAnalysisImages, () => CanSaveAnalysis);
        }

        private bool CanAnalyze()
        {
            return OriginalImage != null && !string.IsNullOrEmpty(_originalImagePath) && !IsAnalyzing;
        }

        private async void AnalyzeParallelism()
        {
            if (!CanAnalyze()) return;

            // 检查是否有ProcessedImage
            if (ProcessedImage == null)
            {
                ParallelismResultText = "请先确保有处理后的图像（调整ROI或阈值）";
                return;
            }

            IsAnalyzing = true;
            AnalysisProgress = 0;
            ParallelismResultText = "基于Processed Image分析曲线...";

            try
            {
                // 从ProcessedImage提取像素数据
                byte[] processedImageData = await Task.Run(() =>
                {
                    var stride = (ProcessedImage.PixelWidth * ProcessedImage.Format.BitsPerPixel + 7) / 8;
                    var pixels = new byte[stride * ProcessedImage.PixelHeight];
                    ProcessedImage.CopyPixels(pixels, stride, 0);
                    return pixels;
                });

                // 准备原始ROI图像数据
                byte[] originalRoiImageData = await Task.Run(() =>
                {
                    var cropped = new CroppedBitmap(OriginalImage, new Int32Rect((int)RoiRect.X, (int)RoiRect.Y, (int)RoiRect.Width, (int)RoiRect.Height));
                    var stride = cropped.PixelWidth * 4;
                    var pixels = new byte[stride * cropped.PixelHeight];
                    cropped.CopyPixels(pixels, stride, 0);
                    return pixels;
                });

                // 准备输出缓冲区
                int bufferSize = ProcessedImage.PixelWidth * ProcessedImage.PixelHeight * 4;
                byte[] outImageBuffer = new byte[bufferSize];

                var bytesWritten = await Task.Run(() =>
                {
                    System.Diagnostics.Debug.WriteLine($"Calling AnalyzeCurvesFromProcessedImage");
                    System.Diagnostics.Debug.WriteLine($"ProcessedImage size: {ProcessedImage.PixelWidth}x{ProcessedImage.PixelHeight}");
                    
                    // 调用更新后的原生函数
                    return NativeMethods.AnalyzeCurvesFromProcessedImage(
                        processedImageData,
                        originalRoiImageData,
                        ProcessedImage.PixelWidth,
                        ProcessedImage.PixelHeight,
                        outImageBuffer,
                        bufferSize
                    );
                });

                if (bytesWritten > 0)
                {
                    // 成功，收到了图像数据
                    ParallelismResultText = $"分析完成，已生成可视化结果。";
                    try
                    {
                        var bitmap = new BitmapImage();
                        using (var ms = new System.IO.MemoryStream(outImageBuffer, 0, bytesWritten))
                        {
                            bitmap.BeginInit();
                            bitmap.CacheOption = BitmapCacheOption.OnLoad;
                            bitmap.StreamSource = ms;
                            bitmap.EndInit();
                        }
                        bitmap.Freeze(); // Freeze for use on UI thread

                        // 使用Dispatcher在UI线程上更新属性
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            RoiRawImage = bitmap; // 将分析结果显示在ROI Raw框中
                            _hasAnalysisResult = true; // 标记有分析结果
                            OnPropertyChanged(nameof(CanSaveAnalysis)); // 通知保存按钮状态变化
                        });
                    }
                    catch (Exception ex)
                    {
                        ParallelismResultText = $"Error decoding result image: {ex.Message}";
                    }
                }
                else
                {
                    // C++端发生错误
                    ParallelismResultText = $"分析失败，错误码: {bytesWritten}";
                }

                AnalysisProgress = 100; // Ensure progress is full on completion
            }
            catch (Exception ex)
            {
                ParallelismResultText = $"Error: {ex.Message}";
                MessageBox.Show($"An error occurred during parallelism analysis: {ex.Message}", "Analysis Error", MessageBoxButton.OK, MessageBoxImage.Error);
                AnalysisProgress = 0; // Reset progress on error
            }
            finally
            {
                IsAnalyzing = false;
            }
        }

        private void UpdateProgress(int progress)
        {
            // Use the dispatcher to update the UI thread safely
            Application.Current.Dispatcher.Invoke(() =>
            {
                if (progress < 0)
                {
                    ParallelismResultText = "An error occurred in the native library.";
                    AnalysisProgress = 0;
                }
                else
                {
                    AnalysisProgress = progress;
                }
            });
        }

        private void LoadImage()
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "Image files (*.png;*.jpeg;*.jpg;*.bmp)|*.png;*.jpeg;*.jpg;*.bmp|All files (*.*)|*.*"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    _originalImagePath = openFileDialog.FileName; // Save the path
                    var uri = new Uri(_originalImagePath);
                    // Load the image into a format that's easy to work with (Bgra32)
                    var bitmap = new BitmapImage(uri);
                    var convertedBitmap = new FormatConvertedBitmap(bitmap, PixelFormats.Bgra32, null, 0);
                    convertedBitmap.Freeze(); // 冻结图像，使其可以在其他线程上安全访问
                    OriginalImage = convertedBitmap;
                    // Reset analysis result flag when a new image is loaded
                    _hasAnalysisResult = false;
                    // Reset ROI when a new image is loaded
                    MaximizeRoi();
                }
                catch (Exception ex)
                {
                    // Handle potential file loading errors
                    System.Windows.MessageBox.Show($"Failed to load image: {ex.Message}", "Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    OriginalImage = null;
                    _originalImagePath = null;
                }
            }
        }

        private void MaximizeRoi()
        {
            if (OriginalImage != null)
            {
                // Set the ROI to the full image size.
                // This will trigger an update via the RoiRect setter.
                RoiRect = new Rect(0, 0, OriginalImage.PixelWidth, OriginalImage.PixelHeight);
            }
        }

        private void SaveAnalysisImages()
        {
            if (!CanSaveAnalysis) return;

            try
            {
                // 使用SaveFileDialog让用户选择保存位置
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "保存分析图像",
                    Filter = "JPEG files (*.jpg)|*.jpg|PNG files (*.png)|*.png|All files (*.*)|*.*",
                    DefaultExt = "jpg",
                    FileName = "analysis_result"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    string baseFileName = System.IO.Path.GetFileNameWithoutExtension(saveFileDialog.FileName);
                    string directory = System.IO.Path.GetDirectoryName(saveFileDialog.FileName) ?? "";
                    string extension = System.IO.Path.GetExtension(saveFileDialog.FileName);

                    // 保存ProcessedImage（处理后的图像）
                    if (ProcessedImage != null)
                    {
                        string processedFileName = System.IO.Path.Combine(directory, $"{baseFileName}_processed{extension}");
                        SaveBitmapSourceToFile(ProcessedImage, processedFileName);
                    }

                    // 保存RoiRawImage（分析结果图像）
                    if (RoiRawImage != null)
                    {
                        string roiRawFileName = System.IO.Path.Combine(directory, $"{baseFileName}_curves{extension}");
                        SaveBitmapSourceToFile(RoiRawImage, roiRawFileName);
                    }

                    ParallelismResultText = $"分析图像已保存到: {directory}";
                }
            }
            catch (Exception ex)
            {
                ParallelismResultText = $"保存失败: {ex.Message}";
            }
        }

        private void SaveBitmapSourceToFile(BitmapSource bitmapSource, string fileName)
        {
            BitmapEncoder encoder;
            string extension = System.IO.Path.GetExtension(fileName).ToLower();

            switch (extension)
            {
                case ".png":
                    encoder = new PngBitmapEncoder();
                    break;
                case ".jpg":
                case ".jpeg":
                default:
                    encoder = new JpegBitmapEncoder { QualityLevel = 90 };
                    break;
            }

            encoder.Frames.Add(BitmapFrame.Create(bitmapSource));

            using (var fileStream = new System.IO.FileStream(fileName, System.IO.FileMode.Create))
            {
                encoder.Save(fileStream);
            }
        }

        private bool _hasAnalysisResult = false; // 标记是否有分析结果

        public bool CanSaveAnalysis => _hasAnalysisResult && ProcessedImage != null && RoiRawImage != null;

        private void UpdateRoiRawImage()
        {
            // 如果有分析结果，不要覆盖它
            if (_hasAnalysisResult) return;

            if (OriginalImage == null || RoiRect.Width <= 0 || RoiRect.Height <= 0)
            {
                RoiRawImage = null;
                return;
            }

            try
            {
                // 从原图中提取ROI区域
                var roiX = (int)Math.Max(0, RoiRect.X);
                var roiY = (int)Math.Max(0, RoiRect.Y);
                var roiWidth = (int)Math.Min(RoiRect.Width, OriginalImage.PixelWidth - roiX);
                var roiHeight = (int)Math.Min(RoiRect.Height, OriginalImage.PixelHeight - roiY);

                if (roiWidth <= 0 || roiHeight <= 0) return;

                // 创建ROI区域的裁剪图像
                var croppedBitmap = new CroppedBitmap(OriginalImage, new Int32Rect(roiX, roiY, roiWidth, roiHeight));
                RoiRawImage = croppedBitmap;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating ROI raw image: {ex.Message}");
                RoiRawImage = null;
            }
        }

        private void ScheduleUpdateProcessedImage()
        {
            // Cancel any existing timer
            _updateTimer?.Stop();

            // Create a new timer with a short delay to debounce rapid updates
            _updateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(100) // 100ms delay
            };
            _updateTimer.Tick += (sender, e) =>
            {
                _updateTimer.Stop();
                UpdateProcessedImage();
            };
            _updateTimer.Start();
        }

        private void UpdateProcessedImage()
        {
            System.Diagnostics.Debug.WriteLine($"UpdateProcessedImage called");
            System.Diagnostics.Debug.WriteLine($"OriginalImage: {OriginalImage != null}");
            System.Diagnostics.Debug.WriteLine($"RoiRect: {RoiRect}");

            if (OriginalImage == null || RoiRect.Width <= 0 || RoiRect.Height <= 0)
            {
                System.Diagnostics.Debug.WriteLine("Skipping processing: Invalid image or ROI");
                ProcessedImage = null;
                return;
            }

            // Validate ROI bounds
            if (RoiRect.X < 0 || RoiRect.Y < 0 ||
                RoiRect.X + RoiRect.Width > OriginalImage.PixelWidth ||
                RoiRect.Y + RoiRect.Height > OriginalImage.PixelHeight)
            {
                System.Diagnostics.Debug.WriteLine("Skipping processing: ROI out of bounds");
                ProcessedImage = null;
                return;
            }

            try
            {
                int fullWidth = OriginalImage.PixelWidth;
                int fullHeight = OriginalImage.PixelHeight;
                int fullStride = fullWidth * 4; // 4 bytes per pixel for Bgra32
                byte[] inputBytes = new byte[fullHeight * fullStride];
                OriginalImage.CopyPixels(inputBytes, fullStride, 0);

                System.Diagnostics.Debug.WriteLine($"Image dimensions: {fullWidth}x{fullHeight}");
                System.Diagnostics.Debug.WriteLine($"Input bytes length: {inputBytes.Length}");

                // The output buffer size now depends on the ROI size.
                byte[] outputBytes = new byte[(int)(RoiRect.Width * RoiRect.Height)];
                int outputWidth, outputHeight;

                // Call the updated C++ function with all parameters
                System.Diagnostics.Debug.WriteLine($"Calling C++ function with parameters:");
                System.Diagnostics.Debug.WriteLine($"  Thresholds: {Threshold1}, {Threshold2}");
                System.Diagnostics.Debug.WriteLine($"  ROI: ({(int)RoiRect.X}, {(int)RoiRect.Y}, {(int)RoiRect.Width}, {(int)RoiRect.Height})");
                System.Diagnostics.Debug.WriteLine($"  Output buffer size: {outputBytes.Length}");

                int result;
                try
                {
                    result = NativeMethods.ProcessImage_Canny(
                        inputBytes,
                        outputBytes,
                        fullWidth,
                        fullHeight,
                        Threshold1,
                        Threshold2,
                        (int)RoiRect.X,
                        (int)RoiRect.Y,
                        (int)RoiRect.Width,
                        (int)RoiRect.Height
                    );
                }
                catch (AccessViolationException ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Access violation in C++ function: {ex.Message}");
                    ProcessedImage = null;
                    return;
                }
                catch (SEHException ex)
                {
                    System.Diagnostics.Debug.WriteLine($"SEH exception in C++ function: {ex.Message}");
                    ProcessedImage = null;
                    return;
                }

                // The C++ function should set these, but for now we'll use ROI dimensions
                outputWidth = (int)RoiRect.Width;
                outputHeight = (int)RoiRect.Height;

                System.Diagnostics.Debug.WriteLine($"C++ function returned: {result}");
                System.Diagnostics.Debug.WriteLine($"Output dimensions: {outputWidth}x{outputHeight}");

                if (outputWidth > 0 && outputHeight > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"Creating BitmapSource with dimensions {outputWidth}x{outputHeight}");
                    // Convert the grayscale byte array back to a BitmapSource
                    ProcessedImage = BitmapSource.Create(
                        outputWidth,
                        outputHeight,
                        OriginalImage.DpiX,
                        OriginalImage.DpiY,
                        PixelFormats.Gray8,
                        null,
                        outputBytes,
                        outputWidth // Stride for grayscale is just the width
                    );
                    ProcessedImage.Freeze(); // Freeze for performance, as it's used across threads
                    System.Diagnostics.Debug.WriteLine("ProcessedImage created successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Invalid output dimensions: {outputWidth}x{outputHeight}");
                    ProcessedImage = null;
                }
            }
            catch (DllNotFoundException ex)
            {
                System.Diagnostics.Debug.WriteLine($"DLL not found: {ex.Message}");
                System.Windows.MessageBox.Show("Error: ImageProcessor.dll not found.\nPlease ensure the C++ project has been built and the DLL is in the correct location.", "DLL Load Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                ProcessedImage = null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Exception in UpdateProcessedImage: {ex}");
                System.Windows.MessageBox.Show($"An error occurred during image processing: {ex.Message}", "Processing Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                ProcessedImage = null;
            }
        }
    }

    // Basic RelayCommand implementation
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute;
            _canExecute = canExecute;
        }

        public bool CanExecute(object? parameter) => _canExecute == null || _canExecute();

        public void Execute(object? parameter) => _execute();
    }
}